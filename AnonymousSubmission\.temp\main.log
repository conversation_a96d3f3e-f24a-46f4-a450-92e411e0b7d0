This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=pdflatex 2025.7.21)  1 AUG 2025 04:19
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"d:/Projects/Papers/AAAI 2025/AAAI 2025-REX-RAG/AnonymousSubmission/main.tex"
(d:/Projects/Papers/AAAI 2025/AAAI 2025-REX-RAG/AnonymousSubmission/main.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (./aaai2026.sty
Package: aaai2026 2026/06/17 AAAI 2026 Submission format

Conference Style for AAAI for LaTeX 2e -- version for submission
\titlebox=\skip51
(d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/psnfss/courier.sty
Package: courier 2020/03/25 PSNFSS-v9.3 (WaS) 
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen149
\Gin@req@width=\dimen150
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip52
\bibsep=\skip53
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count283
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen151
\captionmargin=\dimen152
\caption@leftmargin=\dimen153
\caption@rightmargin=\dimen154
\caption@width=\dimen155
\caption@indent=\dimen156
\caption@parindent=\dimen157
\caption@hangindent=\dimen158
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count284
\c@continuedfloat=\count285
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count286
\float@exts=\toks18
\float@box=\box53
\@float@everytoks=\toks19
\@floatcapt=\box54
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks20
\c@algorithm=\count287
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count288
\c@ALC@line=\count289
\c@ALC@rem=\count290
\c@ALC@depth=\count291
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/newfloat/newfloat.sty
Package: newfloat 2023/10/01 v1.2 Defining new floating environments (AR)
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count292
\lst@gtempboxa=\box55
\lst@token=\toks21
\lst@length=\count293
\lst@currlwidth=\dimen159
\lst@column=\count294
\lst@pos=\count295
\lst@lostspace=\dimen160
\lst@width=\dimen161
\lst@newlines=\count296
\lst@lineno=\count297
\lst@maxwidth=\dimen162
 (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count298
\lst@skipnumbers=\count299
\lst@framebox=\box56
) (d:/Programs/Toolkits/texlive/2025/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)
\@float@every@listing=\toks22
\c@listing=\count300


! LaTeX Error: File `AnonymousSubmission/Pages/#.Title' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: Title)

Enter file name: 
d:/Projects/Papers/AAAI 2025/AAAI 2025-REX-RAG/AnonymousSubmission/main.tex:41: Emergency stop.
<read *> 
         
l.41 \input{AnonymousSubmission/Pages/#.Title}
                                              ^^M
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 4702 strings out of 467816
 76599 string characters out of 5427053
 488366 words of memory out of 5000000
 33333 multiletter control sequences out of 15000+600000
 626825 words of font info for 40 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,0n,65p,370b,266s stack positions out of 10000i,1000n,20000p,200000b,200000s
d:/Projects/Papers/AAAI 2025/AAAI 2025-REX-RAG/AnonymousSubmission/main.tex:41:  ==> Fatal error occurred, no output PDF file produced!
