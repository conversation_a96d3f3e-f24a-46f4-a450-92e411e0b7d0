\begin{abstract}
Reinforcement learning (RL) is emerging as a powerful paradigm for enabling large language models (LLMs) to perform complex reasoning tasks. Recent advances indicate that integrating RL with retrieval-augmented generation (RAG) allows LLMs to dynamically incorporate external knowledge, leading to more informed and robust decision making. However, we identify a critical challenge
% : during training, LLMs frequently become trapped in “dead ends,” prematurely settling on overconfident yet incorrect conclusions, which hinders effective exploration and policy optimization. 
during policy-driven trajectory sampling: LLMs are frequently trapped in unproductive reasoning paths, which we refer to as ``dead ends", committing to overconfident yet incorrect conclusions. This severely hampers exploration and undermines effective policy optimization.
To address this challenge, we propose \textbf{REX-RAG} (Reasoning Exploration with Policy Realignment in Retrieval-Augmented Generation), a novel framework that explores alternative reasoning paths while maintaining rigorous policy learning through principled distributional corrections. Our approach introduces two key innovations: (1) Mixed Sampling Strategy, which combines a novel probe sampling method with exploratory prompts to escape dead ends; and (2) Policy Realignment Mechanism, which employs importance sampling to correct distribution shifts induced by mixed sampling, thereby mitigating gradient estimation bias. We evaluate it on seven question-answering benchmarks, and experimental results demonstrate REX-RAG achieving average performance gains of 6.2\% on Qwen2.5-3B and 3.6\% on Qwen2.5-7B over strong baselines, demonstrating competitive results across multiple datasets.

% To tackle this issue, we propose a novel framework that systematically explores alternative reasoning paths while maintaining rigorous policy learning through principled distributional corrections. Experiments on seven open-domain QA datasets demonstrate the effectiveness of our approach, showing relative gains of 4.8\% on Qwen2.5-3B-Base and 8.2\% on Qwen2.5-7B-Base over strong RAG baselines.
\end{abstract}

% unbiased