\section{Limitation}
We discuss main limitations of our current approach; further details are provided in the Appendix~\ref{}.

\paragraph{Limited Exploration Strategy}
Our method relies on fixed-pool prompt insertion, which, though effective, can be improved. Future work could include model-generated prompts, backtracking-based search, or full-path restructuring for more comprehensive exploration.

\paragraph{Computational Overhead}
The mixed sampling strategy introduces additional trajectories due to difficulty assessment followed by resampling. Though more efficient than uniform oversampling, difficulty-predictive sampling could reduce this overhead but remains challenging.
