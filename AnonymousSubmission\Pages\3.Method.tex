\begin{figure*}[t]  % 使用 figure* 并设置 [t] 表示靠页面顶部
  \centering
  \includegraphics[width=0.96\textwidth]{figures/framework-v2.pdf}
  \caption{Overview of REX-RAG. (a) Overall framework architecture; (b) Mixed Sampling Strategy in Rollout Phase that combines policy and probe sampling; (c) Policy Realignment Mechanism in Update Phase that corrects distribution shift.}
  \label{fig:framework}
\end{figure*}

\section{Method}

% In this section, we introduce the task background and the framework of RLVR(\S\ref{}). We then present the detailed design of the simple yet effective REX-RAG framework, along with the probe distribution used to guide the training of RLVR (\S\ref{}). Finally, we describe the bias correction and dynamic sampling methods used to stabilize the training process (\S\ref{}).


\subsection{Preliminary}

\paragraph{RAG Task Formulation} RAG addresses this limitation of LLMs when answering complex questions that require external knowledge beyond their training data. Formally, given a question $q$ and a golden answer $a$ from a dataset $\mathcal{D} = \{(q_i, a_i)\}_{i=1}^{n}$, the LLM alternates between generation and retrieval. At each step, it generates reasoning text or a search query, which is used to retrieve documents $d = \{d_1, d_2, \dots, d_k\}$ from an external knowledge source $\mathcal{R}$ (\textit{e.g.}, a search engine or database), and produces a final answer.

\paragraph{RLVR Enhanced RAG} RLVR extends the RAG framework by integrating retrieval and reasoning into a reinforcement learning loop~\cite{li2025towards}. The learning process is guided by a verifiable reward signal based on an objective correctness criterion, such as exact match.
Formally, for each question-answer pair $(q,y)$, the reward signal $r(q,y)$ provides feedback indicating whether the generated answer satisfies predefined verification criteria. % (e.g., exact match with ground truth)
% Through this reward-guided learning process, the model is incentivized to develop effective retrieval strategies and reasoning capabilities that maximize the probability of generating verifiably correct answers.

% whether the answer $y$ to a question $q$ matches the ground truth or satisfies predefined checks. RLVR training thus incentivizes the model to both retrieve effectively from $\mathcal{R}$ and generate accurate responses to maximize this reward.

\paragraph{GRPO Algorithm}
\label{sec:grpo}
GRPO~\cite{DeepSeekMath} is an emerging RL algorithm for training LLM policies. 
% It eliminates the need for value networks by leveraging normalized rewards across multiple trajectories as advantage estimates, while ensuring policy stability through clipping and Kullback–Leibler (KL) divergence regularization.
Formally, consider a fixed reference policy $\pi_{\mathrm{ref}}$ (e.g., the pre-trained LLM before reinforcement learning). GRPO trains a target policy LLM $\pi_\theta$ to maximize the expected reward while remaining close to the reference policy for stability. For a given query $q$, GRPO generates multiple trajectories through rollouts and computes a normalized reward as the advantage:
\begin{align}
\hat{A}_{i,t} = \frac{r_{i,t} - \mathrm{mean}(r_t)}{\mathrm{std}(r_t)} \text{.}
\end{align}

% 为了简洁，我们正文中GRPO 相关的表述中，没有区分$\pi_{}$

% The training objective is:
% \begin{align}
% \mathcal{L}_{\text{GRPO}} = \mathbb{E}_{(s,a) \sim \mu} \left[ \min \left( \rho_t \hat{A}_t, \text{clip}(\rho_t, 1-\epsilon, 1+\epsilon) \hat{A}_t \right) \right] - \beta \mathbb{E}_{s \sim \mu} \left[ \text{KL}(\pi_\theta(\cdot|s) \| \pi_{\text{ref}}(\cdot|s)) \right]
% \end{align}

% where $\rho_t = \frac{\pi_\theta(a_t|s_t)}{\mu(a_t|s_t)}$ is the importance ratio.

% 这里应该要改成 GRPO 的优化目标
% \begin{align}
%   \max_{\theta}\; &\mathbb{E}_{(q,a)\sim\mathcal{D},\;y\sim\pi_\theta(\cdot\mid q;\mathcal{R})}\bigl[r(q,y)\bigr] \notag\\
%   & - \beta\,\mathrm{D}_{\mathrm{KL}}\bigl[\pi_\theta(y\mid q;\mathcal{R})\,\|\,\pi_{\mathrm{ref}}(y\mid q;\mathcal{R})\bigr],
% \end{align}


% 将这部分改为 GRPO

\subsection{REX-RAG Framework}

In this work, we propose REX-RAG, a novel framework that addresses the exploration challenge in RLVR-based RAG through two key innovations. As illustrated in Fig. \ref{fig:framework}, during the Rollout Phase (Fig. \ref{fig:framework} (b)), a Mixed Sampling Strategy generates diverse trajectories by combining actions from both the target policy $\pi_\theta$ and a more exploratory probe policy $\pi_\varepsilon$ to escape ``dead ends". In the subsequent Update Phase (Fig. \ref{fig:framework} (c)), a Policy Realignment Mechanism applies importance sampling to correct distribution shifts introduced by mixed sampling,  ensuring stable policy learning while incorporating insights from exploratory rollouts.

\paragraph{RLVR Algorithm} REX-RAG is implemented using GRPO as the underlying reinforcement learning algorithm. As described in Sec. \ref{sec:grpo}, GRPO generates multiple trajectories in Rollout Phase and computes normalized rewards as advantages to update policy parameters in Update Phase.

\paragraph{Structured Interaction Protocol} To facilitate structured interaction between the model and search engine, we adopt the Search-R1 protocol~\cite{Search-r1}, which uses specialized tokens to define different actions during the reasoning process. Specifically, this method use prompt engineering to enables the model to autonomously interact with the search engine through special tokens that trigger different actions. The specific actions are detailed in the appendix.
% These tokens are defined as follows:
% \begin{itemize}
%     \item Search queries are enclosed in \textcolor{blue}{\texttt{<search>}} and \textcolor{blue}{\texttt{</search>}} tags to trigger information retrieval.
%     \item Retrieved contents returned from search engines are presented within \textcolor{orange}{\texttt{<information>}} and \textcolor{orange}{\texttt{</information>}} tags.
%     \item Model's reasoning steps are enclosed in \textcolor{green}{\texttt{<think>}} and \textcolor{green}{\texttt{</think>}} tags.
%     \item Final answers are formatted using \textcolor{red}{\texttt{<answer>}} and \textcolor{red}{\texttt{</answer>}} tags.
% \end{itemize}


% Notably, Do not compute loss


% TODO: Add Format Rewards
\paragraph{Reward Function}The reward function is a rule-based reward using exact match. Specifically, the exact match strictly assigns a reward of 1 if the model’s answer exactly matches the golden answer, and 0 otherwise.
% The reward function includes answer reward and format rhaodeward. The answer reward follows the exact match criterion, while the format reward encourages specified output format. The overall reward is computed as:
\begin{align}
r = \mathrm{EM}(\text{ans}_{\mathrm{pred}}, \text{ans}_{\mathrm{gold}})\text{.}
\end{align}
% where $\text{ans}_{\mathrm{pred}}$ and $\text{ans}_{\mathrm{gold}}$ are the predicted answer extracted from the model's output trajectory and the ground truth answer, respectively.




\subsection{Mixed Sampling Strategy}

The Mixed Sampling Strategy enhances exploration by employing a mixed behavior policy that combines trajectories from both the current policy $\pi_\theta$ and the probe policy $\pi_\varepsilon$, thus, the mixed behavior policy can be formulated as:
\begin{align}
  \mu = \{\pi_\theta, \pi_\varepsilon\}\text{.}
\end{align}
% \begin{align}
%   \mu = f(\pi_\theta, \pi_\varepsilon)
% \end{align}

Specifically, the strategy adaptively samples from both policies to maintain exploration diversity. It operates through a two-stages process: first sampling trajectories from the LLM policy, then adaptively performing probe sampling based on the proportion of incorrect paths.

\paragraph{Adaptive Probe Re-sampling} To effectively balance exploration and exploitation, REX-RAG introduces an adaptive probe re-sampling mechanism that dynamically adjusts the degree of exploration based on the observed performance of the current policy. 

The exploration process begins by sampling $n$ trajectories for each question. After collecting the corresponding rewards $\{r_1, r_2, \dots, r_n\}$, where each $r_i \in [0, 1]$, additional exploratory trajectories are sampled in an adaptive manner. Specifically, each trajectory is resampled with probability $p(1 - r_i)$, where $p \in [0, 1]$ is a hyperparameter that controls sampling ratio. This adaptive mechanism encourages more exploration when the policy underperforms and less when it performs well. Consequently, for each question, the expected number of resampled trajectories is given by:
\begin{align}
    m = p \sum_{i=1}^{n} (1 - r_i)\text{.}
\end{align}




% The exploration process begins by sampling $n$ trajectories for per question. After obtaining rewards ${r_1, r_2, \dots, r_n}$ of these trajectories, where each $r_i \in [0,1]$, additional exploratory trajectories are adaptively sampled. Specifically, each trajectory is resampled with probability equal to $p(1 - r_i)$, where $p \in [0,1]$ is a hyperparameter used to control intensity of resampling. This adaptive approach allocates exploration effort proportionally to policy performance—increasing exploration when the policy struggles and reducing it when it performs well. Thus, for each question, the number of additionally sampled trajectories is $n_{\text{resample}} = p \sum_{i=1}^{n} (1 - r_i)$.

% This mechanism first evaluates the policy through an initial round of sampling, and then selectively injects additional exploratory trajectories where performance is suboptimal.


% the sampling rate $p \in [0,1]$ scaled by its deficiency $(1 - r_i)$, yielding an expected number of resamples $n_{\text{resample}} = p \sum_{i=1}^{n}(1 - r_i)$. Thus, more exploration occurs when policy performance is poor, and less when it performs well.




% Specifically, the process begins by sampling $n$ trajectories from the current policy $\pi_\theta$. After observing the rewards from these initial trajectories, the mechanism determines the number of additional exploratory trajectories. For a set of $n$ sampled rewards $\{r_1, r_2, \dots, r_n\}$ with $r_i \in [0,1]$, the expected number of resampled instances is computed as:
% % \begin{align}
% % n_{\text{resample}} = p \sum_{i=1}^{n} (1 - r_i)
% % \end{align}
% % where $p \in [0,1]$ serves as a hyperparameter that controls the resampling strength. In practice, we compute the resampling probability for each trajectory as $p(1 - r_i)$, and sample additional trajectories accordingly. This adaptive approach ensures that exploration effort is proportionally allocated based on the policy's current performance - increasing exploration when the policy struggles and reducing it when the policy performs well.


\paragraph{Construction of Probe Policy} To enable effective exploration, the probe policy $\pi_\varepsilon$ is constructed using a simple prompt-guided augmentation strategy, which generates exploratory trajectories by injecting exploratory guidance into the original reasoning process.



Each exploratory trajectory $o^{\prime}$ is composed by concatenating three components:
\begin{align}
o^{\prime} = o^{\prime}_{\text{origin}} \oplus o^{\prime}_{\text{prompt}} \oplus o^{\prime}_{\text{probe}}\text{,}
\end{align}
where $\oplus$ denotes sequence concatenation. Specifically:
\begin{itemize}
\item $o^{\prime}_{\text{origin}}$: the original model rollout up to the point where it produces an incorrect or premature answer, preserving the initial reasoning context.
\item $o^{\prime}_{\text{prompt}}$: an exploration prompt sampled from a curated prompt pool $\mathcal{P}$, designed to inject alternative reasoning directions.
\item $o^{\prime}_{\text{probe}}$: a new continuation generated by the target model $\pi_\theta$, conditioned on the modified context.
\end{itemize}

% TODO: Ref

The prompt pool $\mathcal{P}$ is built by rephrasing a comprehensive reflection prompt into $k$ diverse chain-of-thought fragments using GPT-4.5 ~\cite{gpt_4_5}. These fragments represent various reasoning strategies or question reformulations designed to stimulate exploration. The full list of base prompts and their derived fragments are provided in the Appendix~\ref{}. For more empirical results on different prompts, please refer to Appendix \ref{}. 

% Importantly, empirical results in Appendix~\ref{} show that the specific linguistic form of these prompts has minimal effect on performance, demonstrating the robustness of this exploration mechanism to prompt variation.


\subsection{Policy Realignment Mechanism}

% TODO: 将会和下面两段合并
\paragraph{Distribution Shift Chanllenge} If the mismatch between the behavior policy $\mu=\{\pi_\theta,\pi_\varepsilon\}$ and the target policy $\pi_\theta$ introduced by the mixed sampling strategy is not addressed, model-generated samples are systematically underweighted, whereas tokens from exploration prompts are overweighted. As a result, tokens in inserted spans with negative advantages  may be excessively penalized, potentially falling outside $\pi_\theta$'s support, whereas regions with positive advantages risk entropy collapse due to overly concentrated probabilities. Although GRPO's clipping trick partially addresses these issues, it does not apply during the first update in each training step, leaving the problem unresolved. Fundamentally, using an on-policy estimator in an off-policy setting introduces estimation bias and instability. To mitigate this, we propose a \emph{Policy Realignment Mechanism} (Fig.\ref{fig:framework}(c)), which reduces distribution shift and gradient bias via two steps: (i) \textit{Trajectory Filtering}, and (ii) \textit{Distribution Realignment}.

% 详细的理论分析，参见附录

% Mixed sampling inevitably creates a mismatch between the behavior distribution$\mu=\{\pi_\theta, \pi_\varepsilon\}$, and the target policy $\pi_\theta$. Without correction, model-generated samples become systematically underweighted, while samples from exploration prompts and subsequent tokens become overweighted.

% If there is no extra approach to deal with the mismatch between behavior distribution$\mu=\{\pi_\theta, \pi_\varepsilon\}$, and the target policy $\pi_\theta$, introduced by Mixed Sampling Strategy, model-generated samples become systematically underweighted, while samples from exploration prompts and subsequent tokens become overweighted.



% Mixed sampling inevitably introduces a mismatch between the behavior distribution, $\mu= \{ \pi_\theta,\pi_\varepsilon\}$, and the target policy $\pi_\theta$. Absent any correction, samples drawn from the model are systematically underweighted, whereas those arising from inserted exploration prompts, and subsequent tokens—are overweighted. Consequently, tokens with negative advantages within the prompts may be overly penalized, potentially falling outside the support of $\pi_\theta$; conversely, regions with positive advantage may induce entropy collapse by concentrating probability mass on a few tokens. Although the clip trick in GRPO can alleviate these issues, it does not act on the first update of each training step, so the problems persist. Fundamentally, this issue arises from employing an on-policy estimator in an inherently off-policy setting, leading to estimation bias and instability during training. To mitigate this issue, we introduce a \emph{Policy Realignment Mechanism}. As illustrated in Fig.~\ref{fig:framework}(c), this mechanism addresses the distribution shift and minimize bias of policy gradients through two key steps: (i) \textit{Trajectory Filtering}, and (ii) \textit{Distribution Realignment}.



% Mixed sampling inevitably introduces a mismatch between the behavior distribution used to generate trajectories, $\mu={\pi_\theta,\pi_\varepsilon}$, and the target policy $\pi_\theta$ used for updates. Absent any correction, samples drawn from the model are systematically underweighted, whereas those arising from inserted exploration prompts—and subsequent tokens—are overweighted. Consequently, tokens with negative advantages within the inserted spans may be overly penalized, potentially falling outside the support of $\pi_\theta$; conversely, regions with positive advantage may induce entropy collapse by concentrating probability mass on a few tokens. Although the truncation heuristic in GRPO can alleviate these issues, it does not act on the first update of each training step, so the problems persist. Fundamentally, this is the well-known off-policy learning issue of using an on-policy estimator for updates, which induces estimation bias and training instability.

% % 如果 REX-RAG 框架不对插入部分及以后的概率进行重组则会产生系统性的偏差。具体来说，由于我们的行为策略和目标策略之间存在的 shift，会导致在每个 Token 的更新上都出现一个 $\frac{\mu}{\pi}$,继而这些 Token 的 $\frac{\pi}{\pi}$项会被系统性低估，继而某些正优势 Token 原本应在被梯度截断的时候会错误的保持梯度。某些负优势的 Token 则会被提前。而插入及以后的部分，则会出现。而对于插入的。


% 对于模型自由采样的部分会产生系统性忽视和低估。而对于插入及以后的部分，则又会高估。继而，如果插入部分的某些Token处于负优势，很可能会导致这些 Token 直接掉出目标策略的支持集（原本概率就低，而我们又施加了过多的惩罚在上面）。而对于正优势的部分则有可能造成熵值崩塌，也就是目标策略变成单一模式，甚至几个 Token 占据全部的概率质量。虽然会有截断技巧可以缓解这些问题。但是由于在每个训练step 的第一次更新时截断技巧不会生效，这些问题仍然存在。而本质上这些都是由于将实际上的 off-policy 行为，但使用了 on-policy 的更新方法造成的。

% 混合采样不可避免地会在生成轨迹的行为分布 $\mu=\{\pi_\theta,\pi_\varepsilon\}$ 与用于更新的目标策略 $\pi_\theta$ 之间造成不匹配。若不做任何矫正，模型采样的部分会被系统性忽视，而在插入的探索提示及之后则出现过度加权。这样，插入片段中具有负优势的 token 可能被过度惩罚，以至于落到 $\pi_\theta$ 的支持集之外；而正优势区域则可能通过将概率质量集中到少数几个 token 上而引发熵坍塌。GRPO 中的截断技巧虽然可以缓解该问题，但不会作用于每个训练步的首次更新，因此这些问题仍会持续。归根结底，这是在强化学习中早已为人所知的，一个由于用同策略估计器去更新的异策略学习问题，进而导致的估计偏差和训练不稳定。




% To effectively leverage exploratory trajectories without compromising optimization stability, we introduce a \emph{Policy Realignment Mechanism}. As illustrated in Fig.~\ref{fig:framework}(c), this mechanism addresses the distribution shift and minimize bias of policy gradients through two key steps: (i) \textit{Trajectory Filtering}, and (ii) \textit{Distribution Realignment}.

% ensures unbiased policy learning

% \paragraph{Distribution Shift Chanllenge}Incorporating exploratory trajectories generated by a probe policy $\pi_{\varepsilon}$ induces distribution shifts relative to the target policy $\pi_{\theta}$. Because standard on‑policy methods assume trajectories are sampled from $\pi_{\theta}$, naively reusing data collected under the mixed behavior policy $\mu$ would bias gradient estimation and drive suboptimal updates and instability during training.


% Incorporating exploratory trajectories from a probe policy \(\pi_{\varepsilon}\) introduces a distributional mismatch with the target policy \(\pi_{\theta}\) being optimized. Since standard on-policy methods assume that trajectories are sampled from the target policy, this mismatch can bias gradient estimation and suboptimal policy updates. Directly using data from the mixed policy \(\mu\) thus violates the theoretical assumptions of reinforcement learning~\cite{} and leads to instability during training.


\paragraph{Trajectory Filtering} A trajectory filtering mechanism is first introduced to preferentially select rollouts from the probe distribution that closely approximate the target policy, thereby mitigating instability and bias. Specifically, trajectories $o'$ are filtered according to their log-likelihood under the current policy $\pi_\theta$, retaining those consistent enough with it. The retention ratio is controlled by a hyperparameter $\alpha$. After filtering, for each question $t$, the retained trajectories are combined with those generated from the target policy:
% At first, a trajectory filtering mechanism is introduced to select high-quality rollouts from the probe distribution.Since $\pi_\varepsilon$ is designed to explore uncertain paths, many trajectories may be uninformative or misleading. Therefore, trajectoriesed are filtered $o^\prime$ by their log-likelihood under the current policy $\pi_\theta$ , retaining those not overly inconsistent. A hyperparameter $\alpha$ controls the retention ratio. After filtering, for each question $t$, the retained trajectories are combined with those from the target policy:
\begin{align}
  \mathcal{O}_t &= \bigl\{\, o_{i} \mid o_{i} \sim \pi_{\theta} \,\bigr\}_{i=1}^{G} \;\cup\; \bigl\{\, o^{\prime}_{j} \mid o^{\prime}_{j} \sim \pi_{\varepsilon} \,\bigr\}_{j=1}^{\alpha G}\text{.}
\end{align}
% To control the noise introduced by exploratory samples, we keep a fixed proportion $\alpha$ of the filtered probe trajectories, where $\alpha$ is dynamically adjusted via exponential moving average (EMA) based on the observed error rate of the policy.
% \begin{align}
%     \mu = \alpha \pi_{\theta} + (1 - \alpha) \pi_{\varepsilon},
% \end{align}
\paragraph{Distribution Realignment} Despite the trajectory filtering, a significant distributional mismatch still exists between the mixed behavior policy $\mu$ and the target policy $\pi_\theta$. Specifically, we first define the distribution of the Probe Policy through a principled realignment mechanism. Then, leveraging the theory of multiple importance sampling, we derive a gradient estimator from the GRPO optimization objective, and use this estimator to perform parameter updates.

\begin{table*}[htbp]
  \centering
  \caption{Main experimental results on seven QA benchmarks. Best performance is highlighted in \textbf{bold}. $\heartsuit$ denotes in-domain datasets (trained on), $\diamondsuit$ denotes out-of-domain datasets. All results are exact match accuracy.}
  \label{tab:main_results}
  \begin{tabular}{lcccccccc}
    \toprule
    \multirow{2}{*}{Methods}
      & \multicolumn{3}{c}{General QA}
      & \multicolumn{4}{c}{Multi-Hop QA}
      & \multirow{2}{*}{Avg.} \\
    \cmidrule(lr){2-4} \cmidrule(lr){5-8}
      & NQ$^{\heartsuit}$   & TriviaQA$^{\diamondsuit}$   & PopQA$^{\diamondsuit}$   & HotpotQA$^{\heartsuit}$
      & 2wiki$^{\diamondsuit}$ & Musique$^{\diamondsuit}$    & Bamboogle$^{\diamondsuit}$
      & \\
    \midrule
    \multicolumn{9}{l}{\textbf{Qwen2.5-3B-Base/Instruct}} \\
    \hdashline
    \quad RAG                & 34.8 & 54.4 & 38.7 & 25.5 & 22.6 & 4.7 & 0.8 & 27.0 \\
    \quad IRCoT              & 11.1 & 31.2 & 20.0 & 16.4 & 17.1 & 6.7 & 24.0 & 18.1 \\
    \quad Search-o1          & 23.8 & 47.2 & 26.2 & 22.1 & 21.8 & 5.4 & 32.0 & 25.5 \\
    \hdashline
    \quad R1-base            & 22.6 & 45.5 & 17.3 & 20.1 & 26.8 & 5.5 & 22.4 & 22.9 \\
    \quad R1-instruct        & 21.0 & 44.9 & 17.1 & 20.8 & 27.5 & 6.0 & 19.2 & 22.4 \\
    \quad Search-R1-base     & 42.1 & 58.3 & 41.3 & 29.7 & 27.4 & 6.6 & 12.8 & 31.2 \\
    \quad Search-R1-instruct & 34.1 & 54.5 & 37.8 & 32.4 & 31.9 & 10.3 & 26.4 & 32.5 \\
    \hdashline
    \quad \textbf{REX-RAG (Ours)}  & \textbf{43.9} & \textbf{60.4} & \textbf{44.2} & \textbf{37.4} & \textbf{39.7} & \textbf{14.5} & \textbf{31.2} & \textbf{38.7} \\
    \midrule
    \multicolumn{9}{l}{\textbf{Qwen2.5-7B-Base/Instruct}} \\
    \hdashline
    \quad RAG                & 34.9  & 58.5  & 39.2  & 29.9  & 23.5  & 5.8  & 20.8  & 30.4  \\
    \quad IRCoT              & 22.4  & 47.8  & 30.1  & 13.3  & 14.9  & 7.2  & 22.4  & 23.9  \\
    \quad Search-o1          & 15.1  & 44.3  & 13.1  & 18.7  & 17.6  & 5.8  & 29.6  & 20.6  \\
    \hdashline
    \quad R1-base            & 29.7  & 53.9  & 20.2  & 24.2  & 27.3  & 8.3  & 29.6  & 27.6  \\
    \quad R1-instruct        & 27.0  & 53.7  & 19.9  & 23.7  & 29.2  & 7.2  & 29.3  & 27.1  \\
    \quad Search-R1-base     & 39.5  & 56.0  & 38.8  & 32.6  & 27.0  & 12.5  & 36.0  & 35.0  \\
    \quad Search-R1-instruct & 42.9  & 62.3  & 42.7  & 38.6  & 34.6  & 16.2  & 40.0  & 39.6  \\
    \hdashline
    \quad \textbf{REX-RAG (Ours)}  & \textbf{45.5} & \textbf{62.6} & \textbf{44.3} & \textbf{42.2} & \textbf{43.7} & \textbf{19.7} & \textbf{44.8} & \textbf{43.2} \\
    \bottomrule
  \end{tabular}
  \vspace{0.1cm}
  % \footnotesize{ \quad $\ast$ indicates statistically significant improvement over the best baseline (p $<$ 0.05).}
\end{table*}



% 具体来说，我们首先通过有原则的 realignment 机制定义出distribution of Probe Policy。之后使用多重重要性采样理论从 GRPO 的优化目标中得到一个尽量减少偏差的梯度估计，并将其用于我们的参数更新。


% To address this, we introduce a principled Distribution Correction mechanism that estimates and corrects for the divergence between these policies. Specifically, we estimate the trajectory-level likelihood ratio between the two policies and use it to reweight the exploratory samples during training, ensuring the policy updates remain aligned with the target distribution.


% Despite the trajectory filtering, a significant distributional mismatch still exists between the mixed behavior policy $\mu$ and the target policy $\pi_\theta$. To address this, we introduce a principled Distribution Correction mechanism that estimates and corrects for the divergence between these policies. Specifically, we estimate the trajectory-level likelihood ratio between the two policies and use it to reweight the exploratory samples during training, ensuring the policy updates remain aligned with the target distribution.

% Estimation
\textbf{Probe Policy Definition} is nontrivial because the probe policy constructs trajectories by augmenting original rollouts with injected prompts and subsequent continuations. To model \(\pi_{\varepsilon}\) accurately, trajectories are decompossed into segments and model them individually under $\pi_\varepsilon$ as follows:
\begin{align}
  \pi_{\varepsilon}&(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i<t}) = \begin{cases}
    \dfrac{\pi_{\theta}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i<t})}{z^{1/|o'_{\mathrm{origin}}|}},
    & \text{if } o^{\prime}_{i,t} \in o'_{\mathrm{origin}} \\[1.2em]
    \mathrm{PMF}(o^{\prime}_{i<t}, o^{\prime}_{i,t}),
    & \text{if } o^{\prime}_{i,t} \in o'_{\mathrm{prompt}} \\[1.2em]
    \pi_{\theta}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i<t}),
    & \text{if } o^{\prime}_{i,t} \in o'_{\mathrm{probe}}
  \end{cases}\text{.}
\end{align}
\begin{itemize}
\item The prefix segment is treated as sampled from a truncated version of $\pi_\theta$, conditioned on failure, with $z$ representing the empirical failure rate.
\item The prompt segment is deterministically selected, modeled by an empirical probability mass function (PMF) over the prompt pool.
\item The continuation segment is sampled directly from $\pi_\theta$, thus requires no correction.
\end{itemize}

The specific design details and the construction method of the probability mass function based on frequency distribution are provided in the Appendix \ref{}.
% The specific detailed design of probability mass function based on frequency distribution are provided in the appendix \ref{}.

% 具体的设计细节以及基于频率分布的probability mass function的构建方法被提供在附录\ref{}.
% A full derivation of the segment-wise correction and theoretical justification is provided in Appendix~\ref{}.

% A full derivation of the segment-wise correction and theoretical justification is provided in Appendix~\ref{}.

%




% \textbf{Multiple Importance Sampling} is then employed to correct the distributional mismatch between the mixed behavior policy $\mu$, from which data is collected, and the target policy $\pi_{\theta}$, under which the model is optimized. 


% \textbf{Multiple Importance Sampling} is then used to correct for the distributional mismatch. Using \(\pi_{\varepsilon}\) and \(\pi_{\theta}\), we apply multiple importance sampling to correct for the discrepancy between the mixed behavior policy \(\mu\) from which data is collected and the target policy \(\pi_{\theta}\) under which the model is optimized. 


% \begin{align}
%   J(\theta) &=
%   \mathbb{E}_{\substack{(q, a) \sim \mathcal{D}\\
%   (o_i)_{i=1}^{G}\sim \mu(o \mid q)}}
%   \left[
%     \frac{1}{G}\sum_{i=1}^{G}\frac{1}{|o_i|}
%     \sum_{t=1}^{|o_i|}
%     \omega_{i,t}\,
%     \hat{A}_{i,t}
%   \right]
% \end{align}
% The policy gradient of target policy $\pi_{\theta}$ can be formalized as:
 % that reweights samples from behavior policy $\mu$ to estimate gradients for target policy $\pi_{\theta}$
 %
 % that reweights samples from behavior policy $\mu$ to estimate gradients for target policy $\pi_{\theta}$
 %

\textbf{Multiple Importance Sampling} is then further employed to correct the distributional mismatch between the mixed behavior policy $\mu$, from which data is collected, and the target policy $\pi_{\theta}$, under which the model is optimized.

% We use balance-heuristic to compute importance ratio for each action \(o_{i,t}\) at step \(t\) in trajectory \(i\) as:
The importance ratio for action $o_{i,t}$ at time step $t$ within trajectory $i$ is computed according to the balance heuristic ~\cite{BH} as:
\begin{align}
\label{ratio}
\omega_{i,t}
= \frac{(1+\alpha)\,\pi_{\theta}\!\left(o_{i,t} \mid q_i, o_{i,<t}\right)}
{\pi_{\theta}\!\left(o_{i,t} \mid q_i, o_{i,<t}\right) + \alpha\,\pi_{\varepsilon}\!\left(o_{i,t} \mid q_i, o_{i,<t}\right)}\text{.}
\end{align}

The policy is then optimized with the GRPO objective:
\begin{align}
&J_{\text{GRPO}}(\theta)
= \mathbb{E}_{\,q \sim P(Q),\;\{o_i\}_{i=1}^{\mid \mathcal{O}\mid } \sim \mu(\cdot \mid q)} \Bigg[ \frac{1}{\mid \mathcal{O}\mid }\sum_{i=1}^{\mid \mathcal{O}\mid } \frac{1}{|o_i|}\sum_{t=1}^{|o_i|}\\ \notag
&\quad 
\min\!\Bigg(
\omega_{i,t}\,\hat{A}_{i,t},\; \mathrm{clip}\!\left(
\omega_{i,t},
\,1-\varepsilon,\,1+\varepsilon
\right)\hat{A}_{i,t}
\Bigg) \\ \notag
& \quad - \beta\, D_{\mathrm{KL}}\!\left(\pi_{\theta}\,\|\,\pi_{\mathrm{ref}}\right)
\Bigg]\text{,}
\end{align}where the behavior policy was updated to a mixture $\mu$, the coefficient multiplying the advantage was updated to the importance ratio in Eq. (\ref{ratio}) rather than simple ratio between the new and old $\pi_\theta$, and the group size was updated to $\mid \mathcal{O} \mid$.




% where the behavior policy is a mixture $\mu$ rather than $\pi_\theta$, the coefficient multiplying the advantage is the importance ratio in Eq.~(\ref{ratio}), and the Group Size is set to $\mathcal{O}$.

% where the behavior policy is no longer $\pi_\theta$ but a mixture policy $\mu$, and the coefficient preceding the advantage is no longer the simple ratio between the new and old $\pi_\theta$, but the importance ratio defined in Equation (\ref{ratio}).

% 其中，行为策略不再是$\pi_theta$，而是混合分布$\mu$。此外，优势前的系数也不再为简单的新旧$\pi_theta$之间的比值而是Equation \ref{ratio}中的重要性ratio。

% where $\varepsilon>0$ is the clipping parameter, $\beta>0$ controls the KL regularization against the reference policy $\pi_{\mathrm{ref}}$, and $\hat{A}_{i,t}$ denotes the group-relative advantage computed within each set of $G$ outputs for query $q_i$.



% \textbf{Multiple Importance Sampling} is then further employed to correct the distributional mismatch between the mixed behavior policy $\mu$, from which data is collected, and the target policy $\pi_{\theta}$, under which the model is optimized. 



 
% For the sake of analytical simplicity, and by ignoring the KL divergence regularization term and the clipping technique in optimization objective of GRPO~\ref{}, the policy gradients can be simplified as:
% \begin{align}
%   & \nabla_\theta J(\theta) \notag \\
%   &= \mathbb{E}_{\mu} \left[
%     \frac{1}{G} \sum_{i=1}^{G} \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
%     \omega_{i,t}\,
%     \hat{A}_{i,t}\,
%     \nabla_\theta \log\pi_\theta(o_{i,t} \mid q_i, o_{i,<t})
%   \right]\text{,}
% \end{align}
% where $\omega_{i,t}$ denotes the importance sampling ratio. We then use balance heuristic weight to get the importance sampling ratio for each action \(o_{i,t}\) at step \(t\) in trajectory \(i\) as:
% \begin{align}
% \omega_{i,t} = \frac{(1+\alpha)\pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t})}{ \pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t}) + \alpha \pi_{\varepsilon}(o_{i,t} \mid q_i, o_{i,<t})}\text{.}
% \end{align}

% These weights reweight the policy gradient estimates, effectively realigning the exploratory data distribution with the target policy distribution. 


% The final policy gradient is estimated as:
% \begin{align}
%   \hat{g}(\theta) =  \frac{1}{G}\sum^{G}_{i=1}\frac{1}{|o_i|}\sum^{|o_i|}_{t=1} \omega_{i,t} \hat{A}_{i,t}
%   \nabla_\theta \log \pi_\theta(o_{i,t} \mid q_i, o_{i,<t}),
% \end{align}

% The remaining regularization methods in GRPO are jointly employed for parameter updates.

% 基于此表达式，我们相对应的修改了损失han'sh

% 基于此表达式，我们相对应的修改了损失函数并用于

% each action \(o_{i,t}\) at step \(t\) in trajectory \(i\), the importance sampling weight is computed as:
% \begin{align}
%     \omega_{i,t} = \frac{\pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t})}{\alpha \pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t}) + (1 - \alpha) \pi_{\varepsilon}(o_{i,t} \mid q_i, o_{i,<t})}.
% \end{align}
% These weights reweight the policy gradient estimates, effectively realigning the exploratory data distribution with the target policy distribution. The final policy gradient is estimated as:
% \begin{align}
% J(\theta) =
% \mathbb{E}_{\substack{(q, a) \sim \mathcal{D}\\
% (o_i)_{i=1}^{G} \sim \mu(o \mid q)}}
% \left[
% \frac{1}{G} \sum_{i=1}^{G} \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
% \omega_{i,t} \cdot \hat{A}_{i,t}
% \right],
% \end{align}

% \begin{align}
%   & \nabla_\theta J(\theta) \notag \\
%   &= \mathbb{E}_{\mu} \left[
%     \frac{1}{G} \sum_{i=1}^{G} \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
%     \omega_{i,t}\,
%     \hat{A}_{i,t}\,
%     \nabla_\theta \log \pi_\theta(o_{i,t} \mid q_i, o_{i,<t})
%   \right]
% \end{align}
% where \(\hat{A}_{i,t}\) is the advantage estimate at step \(t\), and \(G\) denotes the batch size. This formulation guarantees unbiased gradient estimation despite the use of a mixed sampling policy.


% \subsection{Policy Realignment Mechanism}
% To effectively leverage exploratory trajectories without compromising optimization stability, we introduce a \emph{Policy Realignment Mechanism}. As illustrated in Fig.~\ref{fig:framework}(c), this mechanism addresses the distributional shift introduced by mixed sampling and ensures unbiased policy learning through two key components: (i) \textit{Trajectory Filtering}, and (ii) \textit{Distribution Correction}.

% \paragraph{Distribution Shift Challenge}  
% Incorporating exploratory trajectories from a probe policy \(\pi_{\varepsilon}\) introduces a distributional mismatch with the target policy \(\pi_{\theta}\) being optimized. Since standard policy gradient methods assume that trajectories are sampled from the target policy, this mismatch can bias gradient estimation and hinder convergence. Directly using data from the mixed policy \(\mu\) thus violates the theoretical assumptions of unbiased reinforcement learning~\cite{} and leads to instability during training.

% \paragraph{Trajectory Filtering}  
% Let \( o \) denote the set of all trajectories sampled under the mixed behavior policy \(\mu\), which includes rollouts from both \(\pi_{\theta}\) and \(\pi_{\varepsilon}\). To limit the influence of out-of-distribution samples from the probe policy, we define a subset \( o_{\varepsilon} \subset o \) and apply a filtering procedure to control its contribution.

% Specifically, we construct a filtered trajectory set \( o_{\text{merged}} \) by ensuring the empirical mixture ratio between the two sources does not exceed a fixed threshold \(\alpha \in [0,1]\). The resulting effective rollout policy becomes:
% \begin{align}
%     \mu = \alpha\, \pi_{\theta} + (1 - \alpha)\, \pi_{\varepsilon},
% \end{align}
% where \(\alpha\) determines the relative proportion of standard and exploratory trajectories. This filtering step bounds the divergence from the target distribution, thereby improving the reliability of downstream gradient estimation.

% \paragraph{Distribution Correction}  
% Despite filtering, the remaining mismatch between \(\mu\) and \(\pi_{\theta}\) still requires correction. We address this via importance sampling, for which an accurate estimate of \(\pi_{\varepsilon}(o)\) is essential. Each trajectory \( o' \sim \pi_{\varepsilon} \) is composed of three segments:
% \begin{itemize}
%     \item \textbf{Prefix} (\(o'_{\text{before}}\)): A copied prefix from the original rollout, sampled from a truncated version of \(\pi_{\theta}\) conditioned on failure;
%     \item \textbf{Prompt} (\(o'_{\text{prompt}}\)): An inserted prompt drawn from a predefined prompt pool, chosen deterministically or by a categorical distribution;
%     \item \textbf{Continuation} (\(o'_{\text{after}}\)): A new generation from \(\pi_{\theta}\) conditioned on the modified context.
% \end{itemize}

% The token-level likelihood under the probe policy is thus:
% \begin{align}
% \pi_{\varepsilon}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i,<t}) =
% \begin{cases}
%     \dfrac{\pi_{\theta}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i,<t})}{z^{1/|o'_{\text{before}}|}}, & \text{if } o^{\prime}_{i,t} \in o'_{\text{before}} \\[0.8em]
%     \text{PMF}(o^{\prime}_{i,<t}, o^{\prime}_{i,t}), & \text{if } o^{\prime}_{i,t} \in o'_{\text{prompt}} \\[0.8em]
%     \pi_{\theta}(o^{\prime}_{i,t} \mid q_i, o^{\prime}_{i,<t}), & \text{if } o^{\prime}_{i,t} \in o'_{\text{after}}
% \end{cases}
% \end{align}

% Here, \(z\) denotes the empirical failure rate of the policy on the given query. The prompt likelihood is approximated using a categorical probability mass function (PMF) over the prompt pool, while the continuation is directly modeled by \(\pi_\theta\), requiring no correction.

% This segment-aware modeling enables precise estimation of \(\pi_\varepsilon(o')\) for each trajectory, which is used to compute the importance weight:
% \begin{align}
% \omega_{i,t} = \frac{\pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t})}{\alpha \pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t}) + (1 - \alpha) \pi_{\varepsilon}(o_{i,t} \mid q_i, o_{i,<t})}
% \end{align}

% These weights correct for the distribution shift between the behavior policy \(\mu\) and the target policy \(\pi_{\theta}\), ensuring that learning remains centered on the intended policy distribution.

% \paragraph{Gradient Estimation}  
% Finally, the policy gradient is estimated using these importance weights to reweight the log-probability terms:
% \begin{align}
% J(\theta) =
% \mathbb{E}_{\substack{(q, a) \sim \mathcal{D}\\
% (o_i)_{i=1}^{G} \sim \mu(o \mid q)}}
% \left[
% \frac{1}{G} \sum_{i=1}^{G} \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
% \omega_{i,t} \cdot \hat{A}_{i,t}
% \right],
% \end{align}
% where \(\hat{A}_{i,t}\) denotes the estimated advantage at step \(t\), and \(G\) is the batch size. This formulation ensures unbiased gradient estimation despite using a mixed behavior policy for data collection.

% A full derivation of the segment-wise correction and theoretical justification is provided in Appendix~\ref{appendix:policy_realignment}.

% Using the accurately estimated likelihood of \(\pi_{\varepsilon}\), we apply importance sampling to correct for the discrepancy between the mixed behavior policy \(\mu = \alpha \pi_{\theta} + (1 - \alpha) \pi_{\varepsilon}\) from which data is collected and the target policy \(\pi_{\theta}\) under which the model is optimized.

% For each action \(o_{i,t}\) at step \(t\) in trajectory \(i\), the importance sampling weight is computed as:
% \begin{align}
%     \omega_{i,t} = \frac{\pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t})}{\alpha \pi_{\theta}(o_{i,t} \mid q_i, o_{i,<t}) + (1 - \alpha) \pi_{\varepsilon}(o_{i,t} \mid q_i, o_{i,<t})}.
% \end{align}

% These weights reweight the policy gradient estimates, effectively realigning the exploratory data distribution with the target policy distribution. The final policy gradient is estimated as:
% \begin{align}
% J(\theta) =
% \mathbb{E}_{\substack{(q, a) \sim \mathcal{D}\\
% (o_i)_{i=1}^{G} \sim \mu(o \mid q)}}
% \left[
% \frac{1}{G} \sum_{i=1}^{G} \frac{1}{|o_i|} \sum_{t=1}^{|o_i|}
% \omega_{i,t} \cdot \hat{A}_{i,t}
% \right],
% \end{align}
% where \(\hat{A}_{i,t}\) is the advantage estimate at step \(t\), and \(G\) denotes the batch size. This formulation guarantees unbiased gradient estimation despite the use of a mixed sampling policy.

% Detailed derivations and proofs of these corrections are provided in Appendix~\ref{appendix:policy_realignment}.
